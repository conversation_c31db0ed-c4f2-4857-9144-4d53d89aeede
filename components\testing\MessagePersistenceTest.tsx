import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { useMessagesStore } from '@/stores/messagesStore';
import { useTranslation } from '@/hooks/useTranslation';
import { theme } from '@/constants/theme';

export default function MessagePersistenceTest() {
  const [testResults, setTestResults] = useState<string[]>([]);
  
  const {
    messages,
    sendMessage,
    fetchMessages,
    setCurrentUser,
    createConversation,
  } = useMessagesStore();

  const {
    translateMessage,
    settings,
    toggleAutoTranslate,
    getTranslation,
  } = useTranslation();

  useEffect(() => {
    // Initialize test user
    setCurrentUser({
      id: 'test-user',
      name: 'Test User',
      avatar: 'https://via.placeholder.com/50',
      isOnline: true,
    });
  }, []);

  const runPersistenceTest = async () => {
    const results: string[] = [];
    
    try {
      // Test 1: Create conversation
      const conversationId = await createConversation([
        {
          id: 'test-user',
          name: 'Test User',
          avatar: 'https://via.placeholder.com/50',
          isOnline: true,
        },
        {
          id: 'other-user',
          name: 'Other User',
          avatar: 'https://via.placeholder.com/50',
          isOnline: true,
        }
      ]);
      results.push('✅ Conversation created successfully');

      // Test 2: Send message
      await sendMessage(conversationId, 'Hello, this is a test message!', 'text');
      results.push('✅ Message sent successfully');

      // Test 3: Check if message persists
      const conversationMessages = messages[conversationId];
      if (conversationMessages && conversationMessages.length > 0) {
        results.push('✅ Message persisted in store');
      } else {
        results.push('❌ Message not found in store');
      }

      // Test 4: Send another message
      await sendMessage(conversationId, 'This is another test message', 'text');
      results.push('✅ Second message sent');

      // Test 5: Check message count
      const updatedMessages = messages[conversationId];
      if (updatedMessages && updatedMessages.length >= 2) {
        results.push(`✅ Multiple messages persisted (${updatedMessages.length} messages)`);
      } else {
        results.push('❌ Multiple messages not persisted correctly');
      }

    } catch (error) {
      results.push(`❌ Test failed: ${error}`);
    }

    setTestResults(results);
  };

  const runTranslationTest = async () => {
    const results: string[] = [];
    
    try {
      // Test 1: Check translation service initialization
      results.push('🔄 Testing translation service...');

      // Test 2: Translate a simple message
      const testMessage = 'Hello, how are you?';
      const messageId = 'test-msg-' + Date.now();
      
      const translation = await translateMessage(messageId, testMessage, 'es');
      
      if (translation) {
        results.push('✅ Translation service working');
        results.push(`✅ Original: "${translation.originalText}"`);
        results.push(`✅ Translated: "${translation.translatedText}"`);
        results.push(`✅ Source: ${translation.sourceLanguage} → Target: ${translation.targetLanguage}`);
      } else {
        results.push('❌ Translation failed');
      }

      // Test 3: Check cached translation
      const cachedTranslation = getTranslation(messageId);
      if (cachedTranslation) {
        results.push('✅ Translation cached successfully');
      } else {
        results.push('❌ Translation not cached');
      }

      // Test 4: Test auto-translate toggle
      const originalAutoTranslate = settings.autoTranslate;
      await toggleAutoTranslate();
      if (settings.autoTranslate !== originalAutoTranslate) {
        results.push('✅ Auto-translate toggle working');
        // Restore original setting
        await toggleAutoTranslate();
      } else {
        results.push('❌ Auto-translate toggle not working');
      }

    } catch (error) {
      results.push(`❌ Translation test failed: ${error}`);
    }

    setTestResults(prev => [...prev, ...results]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Message & Translation Tests</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={runPersistenceTest}>
          <Text style={styles.buttonText}>Test Message Persistence</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={runTranslationTest}>
          <Text style={styles.buttonText}>Test Translation</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={[styles.button, styles.clearButton]} onPress={clearResults}>
          <Text style={styles.buttonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>Test Results:</Text>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: theme.colors.background,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 20,
    textAlign: 'center',
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: theme.colors.primary,
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    alignItems: 'center',
  },
  clearButton: {
    backgroundColor: theme.colors.gray500,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: theme.colors.gray100,
    padding: 15,
    borderRadius: 10,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 10,
  },
  resultText: {
    fontSize: 14,
    color: theme.colors.text,
    marginBottom: 5,
    fontFamily: 'monospace',
  },
});
