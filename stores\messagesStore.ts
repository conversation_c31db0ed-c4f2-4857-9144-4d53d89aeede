import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Message, Conversation, User } from '@/types/messaging';
import { pushNotificationService } from '@/services/pushNotifications';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

interface MessagesState {
  // Data
  conversations: Conversation[];
  messages: { [conversationId: string]: Message[] };
  currentUser: User | null;

  // UI State
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;

  // Current conversation state
  activeConversationId: string | null;
  typingUsers: { [conversationId: string]: string[] };

  // Message reactions
  messageReactions: { [messageId: string]: { emoji: string; users: string[]; count: number }[] };

  // Search functionality
  searchResults: Message[];
  isSearching: boolean;

  // Push notifications
  unreadCount: number;

  // Actions
  setCurrentUser: (user: User) => void;
  setActiveConversation: (conversationId: string | null) => void;

  // Conversation management
  fetchConversations: () => Promise<void>;
  createConversation: (participants: User[]) => Promise<string>;

  // Message management
  fetchMessages: (conversationId: string) => Promise<void>;
  sendMessage: (conversationId: string, content: string, type?: Message['type']) => Promise<void>;

  // Real-time updates
  addMessage: (message: Message) => void;
  updateMessageStatus: (messageId: string, status: Message['status']) => void;
  markMessagesAsRead: (conversationId: string) => void;

  // Message reactions
  addReaction: (messageId: string, emoji: string) => void;
  removeReaction: (messageId: string, emoji: string) => void;

  // Search functionality
  searchMessages: (query: string) => Promise<void>;
  clearSearchResults: () => void;

  // Typing indicators
  setTyping: (conversationId: string, userId: string, isTyping: boolean) => void;

  // Connection management
  connect: () => void;
  disconnect: () => void;

  // Push notifications
  initializePushNotifications: () => Promise<void>;
  updateUnreadCount: () => void;

  // Utility
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  conversations: [],
  messages: {},
  currentUser: null,
  isLoading: false,
  isConnected: false,
  error: null,
  activeConversationId: null,
  typingUsers: {},
  messageReactions: {},
  searchResults: [],
  isSearching: false,
  unreadCount: 0,
};

export const useMessagesStore = create<MessagesState>()(
  persist(
    (set, get) => ({
      ...initialState,

      setCurrentUser: (user: User) => {
        set({ currentUser: user });
      },

      setActiveConversation: (conversationId: string | null) => {
        set({ activeConversationId: conversationId });
        
        // Mark messages as read when opening conversation
        if (conversationId) {
          get().markMessagesAsRead(conversationId);
        }
      },

      fetchConversations: async () => {
        set({ isLoading: true, error: null });
        
        try {
          // Mock API call - replace with real API
          const mockConversations: Conversation[] = [
            {
              id: 'conv-1',
              participants: [
                {
                  id: 'user-1',
                  name: 'Sophia',
                  avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
                  isOnline: true,
                },
                get().currentUser!
              ].filter(Boolean),
              lastMessage: {
                id: 'msg-1',
                senderId: 'user-1',
                receiverId: get().currentUser?.id || '',
                content: 'Hey! Thanks for the like 😊',
                type: 'text',
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
                status: 'delivered',
              },
              unreadCount: 2,
              isTyping: false,
              typingUsers: [],
            },
            {
              id: 'conv-2',
              participants: [
                {
                  id: 'user-2',
                  name: 'Emma',
                  avatar: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
                  isOnline: false,
                  lastSeen: new Date(Date.now() - 30 * 60 * 1000),
                },
                get().currentUser!
              ].filter(Boolean),
              lastMessage: {
                id: 'msg-2',
                senderId: get().currentUser?.id || '',
                receiverId: 'user-2',
                content: 'Would love to meet up sometime!',
                type: 'text',
                timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
                status: 'read',
              },
              unreadCount: 0,
              isTyping: false,
              typingUsers: [],
            },
          ];

          set({ conversations: mockConversations, isLoading: false });
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch conversations',
            isLoading: false 
          });
        }
      },

      createConversation: async (participants: User[]): Promise<string> => {
        try {
          const conversationId = `conv-${Date.now()}`;
          const newConversation: Conversation = {
            id: conversationId,
            participants,
            unreadCount: 0,
            isTyping: false,
            typingUsers: [],
          };

          set((state) => ({
            conversations: [newConversation, ...state.conversations],
          }));

          return conversationId;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to create conversation' });
          throw error;
        }
      },

      fetchMessages: async (conversationId: string) => {
        set({ isLoading: true, error: null });

        try {
          const state = get();

          // Check if messages already exist in store (from persistence)
          const existingMessages = state.messages[conversationId];

          if (existingMessages && existingMessages.length > 0) {
            // Messages already loaded from persistence, just update loading state
            set({ isLoading: false });
            return;
          }

          // If no existing messages, load initial mock data for demo
          // In production, this would be an API call to fetch conversation history
          const mockMessages: Message[] = [
            {
              id: 'msg-initial-1',
              senderId: 'user-1',
              receiverId: state.currentUser?.id || '',
              content: 'Hey! How are you doing?',
              type: 'text',
              timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
              status: 'read',
            },
            {
              id: 'msg-initial-2',
              senderId: state.currentUser?.id || '',
              receiverId: 'user-1',
              content: 'I\'m doing great! Thanks for asking 😊',
              type: 'text',
              timestamp: new Date(Date.now() - 2.5 * 60 * 60 * 1000),
              status: 'read',
            },
            {
              id: 'msg-initial-3',
              senderId: 'user-1',
              receiverId: state.currentUser?.id || '',
              content: 'That\'s awesome! Want to grab coffee this weekend?',
              type: 'text',
              timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
              status: 'delivered',
            },
          ];

          // Only add mock messages if no messages exist at all
          set((state) => ({
            messages: {
              ...state.messages,
              [conversationId]: mockMessages,
            },
            isLoading: false,
          }));
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch messages',
            isLoading: false
          });
        }
      },

      sendMessage: async (conversationId: string, content: string, type: Message['type'] = 'text') => {
        const state = get();
        if (!state.currentUser) return;

        // Haptic feedback
        if (Platform.OS !== 'web') {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }

        // Find the conversation to get receiver ID
        const conversation = state.conversations.find(conv => conv.id === conversationId);
        const receiverId = conversation?.participants.find(p => p.id !== state.currentUser?.id)?.id || '';

        const message: Message = {
          id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          senderId: state.currentUser.id,
          receiverId,
          content,
          type,
          timestamp: new Date(),
          status: 'sending',
        };

        // Add message optimistically and update conversation
        set((state) => ({
          messages: {
            ...state.messages,
            [conversationId]: [...(state.messages[conversationId] || []), message],
          },
          conversations: state.conversations.map(conv =>
            conv.id === conversationId
              ? { ...conv, lastMessage: message }
              : conv
          ),
        }));

        try {
          // Send via WebSocket or API
          // For now, simulate success with proper status updates
          setTimeout(() => {
            get().updateMessageStatus(message.id, 'sent');
            setTimeout(() => {
              get().updateMessageStatus(message.id, 'delivered');
            }, 1000);
          }, 500);

        } catch (error) {
          // Revert message status on error
          get().updateMessageStatus(message.id, 'sending');
          set({ error: error instanceof Error ? error.message : 'Failed to send message' });
        }
      },

      addMessage: (message: Message) => {
        const conversationId = get().conversations.find(conv =>
          conv.participants.some(p => p.id === message.senderId)
        )?.id;

        if (!conversationId) return;

        const state = get();
        const isFromCurrentUser = message.senderId === state.currentUser?.id;

        set((state) => ({
          messages: {
            ...state.messages,
            [conversationId]: [...(state.messages[conversationId] || []), message],
          },
          conversations: state.conversations.map(conv =>
            conv.id === conversationId
              ? {
                  ...conv,
                  lastMessage: message,
                  unreadCount: !isFromCurrentUser
                    ? conv.unreadCount + 1
                    : conv.unreadCount
                }
              : conv
          ),
          unreadCount: !isFromCurrentUser ? state.unreadCount + 1 : state.unreadCount,
        }));

        // Trigger push notification for incoming messages
        if (!isFromCurrentUser) {
          const sender = state.conversations
            .find(conv => conv.id === conversationId)
            ?.participants.find(p => p.id === message.senderId);

          if (sender) {
            pushNotificationService.scheduleMessageNotification(message, sender, conversationId);
          }

          // Haptic feedback for incoming message
          if (Platform.OS !== 'web') {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          }
        }
      },

      updateMessageStatus: (messageId: string, status: Message['status']) => {
        set((state) => {
          const updatedMessages = { ...state.messages };
          
          Object.keys(updatedMessages).forEach(conversationId => {
            updatedMessages[conversationId] = updatedMessages[conversationId].map(msg =>
              msg.id === messageId ? { ...msg, status } : msg
            );
          });

          return { messages: updatedMessages };
        });
      },

      markMessagesAsRead: (conversationId: string) => {
        const state = get();
        if (!state.currentUser) return;

        const conversation = state.conversations.find(conv => conv.id === conversationId);
        const unreadCountToReduce = conversation?.unreadCount || 0;

        // Update message statuses to read
        set((state) => ({
          messages: {
            ...state.messages,
            [conversationId]: (state.messages[conversationId] || []).map(msg =>
              msg.receiverId === state.currentUser?.id ? { ...msg, status: 'read' } : msg
            ),
          },
          conversations: state.conversations.map(conv =>
            conv.id === conversationId ? { ...conv, unreadCount: 0 } : conv
          ),
          unreadCount: Math.max(0, state.unreadCount - unreadCountToReduce),
        }));

        // Clear badge if no unread messages
        if (get().unreadCount === 0) {
          pushNotificationService.clearBadge();
        }
      },

      // Message reactions
      addReaction: (messageId: string, emoji: string) => {
        const state = get();
        if (!state.currentUser) return;

        const currentReactions = state.messageReactions[messageId] || [];
        const existingReaction = currentReactions.find(r => r.emoji === emoji);

        if (existingReaction) {
          // Add user to existing reaction
          if (!existingReaction.users.includes(state.currentUser.id)) {
            existingReaction.users.push(state.currentUser.id);
            existingReaction.count++;
          }
        } else {
          // Create new reaction
          currentReactions.push({
            emoji,
            users: [state.currentUser.id],
            count: 1,
          });
        }

        set((state) => ({
          messageReactions: {
            ...state.messageReactions,
            [messageId]: currentReactions,
          },
        }));

        // Haptic feedback
        if (Platform.OS !== 'web') {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
      },

      removeReaction: (messageId: string, emoji: string) => {
        const state = get();
        if (!state.currentUser) return;

        const currentReactions = state.messageReactions[messageId] || [];
        const reactionIndex = currentReactions.findIndex(r => r.emoji === emoji);

        if (reactionIndex !== -1) {
          const reaction = currentReactions[reactionIndex];
          const userIndex = reaction.users.indexOf(state.currentUser.id);

          if (userIndex !== -1) {
            reaction.users.splice(userIndex, 1);
            reaction.count--;

            // Remove reaction if no users left
            if (reaction.count === 0) {
              currentReactions.splice(reactionIndex, 1);
            }
          }
        }

        set((state) => ({
          messageReactions: {
            ...state.messageReactions,
            [messageId]: currentReactions,
          },
        }));
      },

      setTyping: (conversationId: string, userId: string, isTyping: boolean) => {
        set((state) => {
          const currentTyping = state.typingUsers[conversationId] || [];
          const newTyping = isTyping
            ? [...currentTyping.filter(id => id !== userId), userId]
            : currentTyping.filter(id => id !== userId);

          return {
            typingUsers: {
              ...state.typingUsers,
              [conversationId]: newTyping,
            },
            conversations: state.conversations.map(conv =>
              conv.id === conversationId
                ? { ...conv, isTyping: newTyping.length > 0, typingUsers: newTyping }
                : conv
            ),
          };
        });
      },

      connect: () => {
        // Initialize WebSocket connection
        set({ isConnected: true });
      },

      disconnect: () => {
        set({ isConnected: false });
      },

      // Search functionality
      searchMessages: async (query: string) => {
        if (!query.trim()) {
          set({ searchResults: [], isSearching: false });
          return;
        }

        set({ isSearching: true });

        try {
          const state = get();
          const results: Message[] = [];
          const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);

          // Search through all messages
          Object.values(state.messages).forEach(conversationMessages => {
            conversationMessages.forEach(message => {
              if (message.type === 'text' && message.content) {
                const content = message.content.toLowerCase();
                const matchesAllTerms = searchTerms.every(term => content.includes(term));

                if (matchesAllTerms) {
                  results.push(message);
                }
              }
            });
          });

          // Sort by relevance and recency
          results.sort((a, b) => {
            const aExactMatch = a.content.toLowerCase().includes(query.toLowerCase());
            const bExactMatch = b.content.toLowerCase().includes(query.toLowerCase());

            if (aExactMatch && !bExactMatch) return -1;
            if (!aExactMatch && bExactMatch) return 1;

            return b.timestamp.getTime() - a.timestamp.getTime();
          });

          set({ searchResults: results, isSearching: false });
        } catch (error) {
          console.error('Search error:', error);
          set({ searchResults: [], isSearching: false, error: 'Search failed' });
        }
      },

      clearSearchResults: () => {
        set({ searchResults: [], isSearching: false });
      },

      // Push notifications
      initializePushNotifications: async () => {
        try {
          await pushNotificationService.initialize();
        } catch (error) {
          console.error('Failed to initialize push notifications:', error);
        }
      },

      updateUnreadCount: () => {
        const state = get();
        const totalUnread = state.conversations.reduce((total, conv) => total + conv.unreadCount, 0);
        set({ unreadCount: totalUnread });
      },

      clearError: () => set({ error: null }),
      
      reset: () => set(initialState),
    }),
    {
      name: 'messages-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        conversations: state.conversations,
        messages: state.messages,
        currentUser: state.currentUser,
        messageReactions: state.messageReactions,
        unreadCount: state.unreadCount,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Convert timestamp strings back to Date objects
          state.conversations = state.conversations.map(conv => ({
            ...conv,
            lastMessage: conv.lastMessage ? {
              ...conv.lastMessage,
              timestamp: new Date(conv.lastMessage.timestamp)
            } : conv.lastMessage
          }));

          // Convert message timestamps
          Object.keys(state.messages).forEach(conversationId => {
            state.messages[conversationId] = state.messages[conversationId].map(msg => ({
              ...msg,
              timestamp: new Date(msg.timestamp)
            }));
          });
        }
      },
    }
  )
);
