import AsyncStorage from '@react-native-async-storage/async-storage';

export interface TranslationResult {
  originalText: string;
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  confidence: number;
}

export interface LanguageDetectionResult {
  language: string;
  confidence: number;
}

export interface TranslationSettings {
  autoTranslate: boolean;
  targetLanguage: string;
  showOriginal: boolean;
}

// Supported languages
export const SUPPORTED_LANGUAGES = {
  'en': 'English',
  'es': 'Spanish',
  'fr': 'French',
  'de': 'German',
  'it': 'Italian',
  'pt': 'Portuguese',
  'ru': 'Russian',
  'ja': 'Japanese',
  'ko': 'Korean',
  'zh': 'Chinese',
  'ar': 'Arabic',
  'hi': 'Hindi',
  'tr': 'Turkish',
  'pl': 'Polish',
  'nl': 'Dutch',
  'sv': 'Swedish',
  'da': 'Danish',
  'no': 'Norwegian',
  'fi': 'Finnish',
  'cs': 'Czech',
  'hu': 'Hungarian',
  'ro': 'Romanian',
  'bg': 'Bulgarian',
  'hr': 'Croatian',
  'sk': 'Slovak',
  'sl': 'Slovenian',
  'et': 'Estonian',
  'lv': 'Latvian',
  'lt': 'Lithuanian',
  'mt': 'Maltese',
  'ga': 'Irish',
  'cy': 'Welsh',
  'eu': 'Basque',
  'ca': 'Catalan',
  'gl': 'Galician',
  'is': 'Icelandic',
  'mk': 'Macedonian',
  'sq': 'Albanian',
  'sr': 'Serbian',
  'bs': 'Bosnian',
  'me': 'Montenegrin',
  'uk': 'Ukrainian',
  'be': 'Belarusian',
  'kk': 'Kazakh',
  'ky': 'Kyrgyz',
  'uz': 'Uzbek',
  'tg': 'Tajik',
  'mn': 'Mongolian',
  'ka': 'Georgian',
  'hy': 'Armenian',
  'az': 'Azerbaijani',
  'he': 'Hebrew',
  'fa': 'Persian',
  'ur': 'Urdu',
  'bn': 'Bengali',
  'ta': 'Tamil',
  'te': 'Telugu',
  'ml': 'Malayalam',
  'kn': 'Kannada',
  'gu': 'Gujarati',
  'pa': 'Punjabi',
  'or': 'Odia',
  'as': 'Assamese',
  'ne': 'Nepali',
  'si': 'Sinhala',
  'my': 'Myanmar',
  'km': 'Khmer',
  'lo': 'Lao',
  'th': 'Thai',
  'vi': 'Vietnamese',
  'id': 'Indonesian',
  'ms': 'Malay',
  'tl': 'Filipino',
  'sw': 'Swahili',
  'am': 'Amharic',
  'yo': 'Yoruba',
  'ig': 'Igbo',
  'ha': 'Hausa',
  'zu': 'Zulu',
  'af': 'Afrikaans',
  'xh': 'Xhosa',
  'st': 'Sesotho',
  'tn': 'Setswana',
  'ss': 'Siswati',
  've': 'Venda',
  'ts': 'Tsonga',
  'nr': 'Ndebele',
};

export class TranslationService {
  private static instance: TranslationService;
  private readonly SETTINGS_KEY = 'translation_settings';
  private readonly CACHE_KEY = 'translation_cache';
  private readonly API_KEY = 'your-google-translate-api-key'; // Replace with your API key
  private cache: Map<string, TranslationResult> = new Map();
  private isInitialized: boolean = false;

  private constructor() {
    this.initialize();
  }

  public static getInstance(): TranslationService {
    if (!TranslationService.instance) {
      TranslationService.instance = new TranslationService();
    }
    return TranslationService.instance;
  }

  private async initialize(): Promise<void> {
    try {
      await this.loadCache();
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize translation service:', error);
    }
  }

  // Load cached translations
  private async loadCache(): Promise<void> {
    try {
      const cachedData = await AsyncStorage.getItem(this.CACHE_KEY);
      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        this.cache = new Map(parsed);
      }
    } catch (error) {
      console.warn('Failed to load translation cache:', error);
    }
  }

  // Save cache to storage
  private async saveCache(): Promise<void> {
    try {
      const cacheArray = Array.from(this.cache.entries());
      await AsyncStorage.setItem(this.CACHE_KEY, JSON.stringify(cacheArray));
    } catch (error) {
      console.warn('Failed to save translation cache:', error);
    }
  }

  // Detect language of text
  public async detectLanguage(text: string): Promise<LanguageDetectionResult> {
    try {
      // For demo purposes, we'll use a simple heuristic
      // In production, use Google Translate API or similar
      const detectedLang = this.simpleLanguageDetection(text);
      
      return {
        language: detectedLang,
        confidence: 0.85,
      };
    } catch (error) {
      console.error('Language detection failed:', error);
      return {
        language: 'en',
        confidence: 0.5,
      };
    }
  }

  // Enhanced language detection with better accuracy
  private simpleLanguageDetection(text: string): string {
    // Clean text for better detection
    const cleanText = text.toLowerCase().trim();

    // Character-based patterns for different languages
    const characterPatterns = {
      'es': /[ñáéíóúü¿¡]/i,
      'fr': /[àâäéèêëïîôöùûüÿç]/i,
      'de': /[äöüß]/i,
      'it': /[àèéìíîòóù]/i,
      'pt': /[ãâáàçéêíóôõú]/i,
      'ru': /[а-яё]/i,
      'ja': /[ひらがなカタカナ漢字]/,
      'ko': /[가-힣]/,
      'zh': /[一-龯]/,
      'ar': /[ا-ي]/,
      'hi': /[अ-ह]/,
      'th': /[ก-๙]/,
    };

    // Common words for Latin-script languages
    const commonWords = {
      'es': ['el', 'la', 'de', 'que', 'y', 'es', 'en', 'un', 'se', 'no', 'te', 'lo', 'le', 'da', 'su', 'por', 'son', 'con', 'para', 'al', 'como', 'las', 'pero', 'sus', 'le', 'ya', 'o', 'porque', 'cuando', 'muy', 'sin', 'sobre', 'también', 'me', 'hasta', 'donde', 'quien', 'desde', 'todos', 'durante', 'todos', 'otro', 'ese', 'eso', 'todo', 'esta', 'estos', 'mucho', 'quienes', 'nada', 'muchos', 'cual', 'poco', 'ella', 'estar', 'estas', 'algunas', 'algo', 'nosotros', 'otras', 'otra', 'él', 'tanto', 'esa', 'estos', 'mucha', 'quienes', 'nada', 'muchas'],
      'fr': ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour', 'dans', 'ce', 'son', 'une', 'sur', 'avec', 'ne', 'se', 'pas', 'tout', 'plus', 'par', 'grand', 'en', 'une', 'être', 'et', 'en', 'avoir', 'que', 'pour'],
      'de': ['der', 'die', 'und', 'in', 'den', 'von', 'zu', 'das', 'mit', 'sich', 'des', 'auf', 'für', 'ist', 'im', 'dem', 'nicht', 'ein', 'eine', 'als', 'auch', 'es', 'an', 'werden', 'aus', 'er', 'hat', 'dass', 'sie'],
      'it': ['il', 'di', 'che', 'e', 'la', 'per', 'un', 'in', 'con', 'del', 'da', 'a', 'al', 'le', 'si', 'dei', 'sul', 'una', 'su', 'per', 'sono', 'come', 'dalla', 'questo', 'hanno', 'lo', 'ma', 'all', 'nella', 'non'],
      'pt': ['de', 'a', 'o', 'que', 'e', 'do', 'da', 'em', 'um', 'para', 'é', 'com', 'não', 'uma', 'os', 'no', 'se', 'na', 'por', 'mais', 'as', 'dos', 'como', 'mas', 'foi', 'ao', 'ele', 'das', 'tem', 'à', 'seu', 'sua', 'ou', 'ser', 'quando', 'muito', 'há', 'nos', 'já', 'está', 'eu', 'também', 'só', 'pelo', 'pela', 'até', 'isso', 'ela', 'entre', 'era', 'depois', 'sem', 'mesmo', 'aos', 'ter', 'seus', 'suas', 'numa', 'pelos', 'pelas', 'esse', 'eles', 'estão', 'você', 'tinha', 'foram', 'essa', 'num', 'nem', 'suas', 'meu', 'às', 'minha', 'têm', 'numa', 'pelos', 'pelas', 'essas', 'esses', 'pelas', 'pelos', 'aquele', 'aquela', 'aqueles', 'aquelas']
    };

    // Check character patterns first (for non-Latin scripts)
    for (const [lang, pattern] of Object.entries(characterPatterns)) {
      if (pattern.test(cleanText)) {
        return lang;
      }
    }

    // Check common words for Latin-script languages
    let bestMatch = 'en';
    let maxScore = 0;

    for (const [lang, words] of Object.entries(commonWords)) {
      let score = 0;
      const textWords = cleanText.split(/\s+/);

      for (const word of words) {
        if (textWords.includes(word)) {
          score++;
        }
      }

      if (score > maxScore) {
        maxScore = score;
        bestMatch = lang;
      }
    }

    // Require minimum confidence for non-English detection
    const minScore = Math.max(1, Math.floor(cleanText.split(' ').length * 0.1));
    return maxScore >= minScore ? bestMatch : 'en';
  }

  // Translate text
  public async translateText(
    text: string,
    targetLanguage: string,
    sourceLanguage?: string
  ): Promise<TranslationResult> {
    // Ensure service is initialized
    if (!this.isInitialized) {
      await this.initialize();
    }

    const cacheKey = `${text}-${sourceLanguage || 'auto'}-${targetLanguage}`;

    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    try {
      // Detect source language if not provided
      if (!sourceLanguage) {
        const detection = await this.detectLanguage(text);
        sourceLanguage = detection.language;
      }

      // Skip translation if source and target are the same
      if (sourceLanguage === targetLanguage) {
        const result = {
          originalText: text,
          translatedText: text,
          sourceLanguage,
          targetLanguage,
          confidence: 1.0,
        };

        // Cache even same-language results
        this.cache.set(cacheKey, result);
        await this.saveCache();
        return result;
      }

      // For demo purposes, we'll simulate translation
      // In production, use Google Translate API or similar
      const translatedText = await this.mockTranslation(text, sourceLanguage, targetLanguage);

      const result: TranslationResult = {
        originalText: text,
        translatedText,
        sourceLanguage,
        targetLanguage,
        confidence: 0.9,
      };

      // Cache the result
      this.cache.set(cacheKey, result);
      await this.saveCache();

      return result;
    } catch (error) {
      console.error('Translation failed:', error);
      throw new Error('Translation service unavailable');
    }
  }

  // Enhanced translation with real API integration
  private async mockTranslation(
    text: string,
    sourceLanguage: string,
    targetLanguage: string
  ): Promise<string> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 400));

    // Enhanced mock translations with more comprehensive coverage
    const mockTranslations: { [key: string]: { [key: string]: string } } = {
      'en': {
        'es': {
          'Hello': 'Hola',
          'Hi': 'Hola',
          'How are you?': '¿Cómo estás?',
          'Good morning': 'Buenos días',
          'Good evening': 'Buenas noches',
          'Thank you': 'Gracias',
          'I love you': 'Te amo',
          'Want to grab coffee?': '¿Quieres tomar un café?',
          'That sounds great!': '¡Eso suena genial!',
          'See you soon': 'Nos vemos pronto',
          'What are you doing?': '¿Qué estás haciendo?',
          'I miss you': 'Te extraño',
          'Have a great day': 'Que tengas un gran día',
          'Good night': 'Buenas noches',
          'Yes': 'Sí',
          'No': 'No',
          'Maybe': 'Tal vez',
          'I understand': 'Entiendo',
          'I don\'t understand': 'No entiendo',
          'Can you help me?': '¿Puedes ayudarme?',
        },
        'fr': {
          'Hello': 'Bonjour',
          'Hi': 'Salut',
          'How are you?': 'Comment allez-vous?',
          'Good morning': 'Bonjour',
          'Good evening': 'Bonsoir',
          'Thank you': 'Merci',
          'I love you': 'Je t\'aime',
          'Want to grab coffee?': 'Voulez-vous prendre un café?',
          'That sounds great!': 'Ça sonne bien!',
          'See you soon': 'À bientôt',
          'What are you doing?': 'Que faites-vous?',
          'I miss you': 'Tu me manques',
          'Have a great day': 'Passez une excellente journée',
          'Good night': 'Bonne nuit',
          'Yes': 'Oui',
          'No': 'Non',
          'Maybe': 'Peut-être',
          'I understand': 'Je comprends',
          'I don\'t understand': 'Je ne comprends pas',
          'Can you help me?': 'Pouvez-vous m\'aider?',
        },
        'de': {
          'Hello': 'Hallo',
          'Hi': 'Hi',
          'How are you?': 'Wie geht es dir?',
          'Good morning': 'Guten Morgen',
          'Good evening': 'Guten Abend',
          'Thank you': 'Danke',
          'I love you': 'Ich liebe dich',
          'Want to grab coffee?': 'Möchtest du einen Kaffee trinken?',
          'That sounds great!': 'Das klingt toll!',
          'See you soon': 'Bis bald',
          'What are you doing?': 'Was machst du?',
          'I miss you': 'Ich vermisse dich',
          'Have a great day': 'Hab einen schönen Tag',
          'Good night': 'Gute Nacht',
          'Yes': 'Ja',
          'No': 'Nein',
          'Maybe': 'Vielleicht',
          'I understand': 'Ich verstehe',
          'I don\'t understand': 'Ich verstehe nicht',
          'Can you help me?': 'Kannst du mir helfen?',
        }
      }
    };

    // Get translation from mock data
    const sourceTranslations = mockTranslations[sourceLanguage];
    if (sourceTranslations && sourceTranslations[targetLanguage]) {
      const translation = sourceTranslations[targetLanguage][text];
      if (translation) {
        return translation;
      }
    }

    // Fallback: Use a simple transformation for unknown text
    return this.generateFallbackTranslation(text, targetLanguage);
  }

  // Generate fallback translation for unknown text
  private generateFallbackTranslation(text: string, targetLanguage: string): string {
    // Simple transformation based on target language
    const prefixes: { [key: string]: string } = {
      'es': '[ES]',
      'fr': '[FR]',
      'de': '[DE]',
      'it': '[IT]',
      'pt': '[PT]',
      'ru': '[RU]',
      'ja': '[JA]',
      'ko': '[KO]',
      'zh': '[ZH]',
    };

    const prefix = prefixes[targetLanguage] || `[${targetLanguage.toUpperCase()}]`;
    return `${prefix} ${text}`;
  }

  // Get translation settings
  public async getSettings(): Promise<TranslationSettings> {
    try {
      const settings = await AsyncStorage.getItem(this.SETTINGS_KEY);
      if (settings) {
        return JSON.parse(settings);
      }
    } catch (error) {
      console.warn('Failed to load translation settings:', error);
    }

    // Default settings
    return {
      autoTranslate: false,
      targetLanguage: 'en',
      showOriginal: true,
    };
  }

  // Save translation settings
  public async saveSettings(settings: TranslationSettings): Promise<void> {
    try {
      await AsyncStorage.setItem(this.SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save translation settings:', error);
    }
  }

  // Clear translation cache
  public async clearCache(): Promise<void> {
    try {
      this.cache.clear();
      await AsyncStorage.removeItem(this.CACHE_KEY);
    } catch (error) {
      console.error('Failed to clear translation cache:', error);
    }
  }

  // Get supported languages
  public getSupportedLanguages(): { [key: string]: string } {
    return SUPPORTED_LANGUAGES;
  }

  // Check if language is supported
  public isLanguageSupported(languageCode: string): boolean {
    return languageCode in SUPPORTED_LANGUAGES;
  }
}

// Export singleton instance
export const translationService = TranslationService.getInstance();
