import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Text, SafeAreaView } from 'react-native';
import MessagePersistenceTest from '@/components/testing/MessagePersistenceTest';
import MatchesTest from '@/components/testing/MatchesTest';
import { theme } from '@/constants/theme';

export default function TestPage() {
  const [activeTest, setActiveTest] = useState<'messages' | 'matches'>('messages');

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTest === 'messages' && styles.activeTab]}
          onPress={() => setActiveTest('messages')}
        >
          <Text style={[styles.tabText, activeTest === 'messages' && styles.activeTabText]}>
            Messages Tests
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTest === 'matches' && styles.activeTab]}
          onPress={() => setActiveTest('matches')}
        >
          <Text style={[styles.tabText, activeTest === 'matches' && styles.activeTabText]}>
            Matches Tests
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {activeTest === 'messages' ? (
          <MessagePersistenceTest />
        ) : (
          <MatchesTest />
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: theme.colors.gray100,
    margin: 20,
    borderRadius: 10,
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: theme.colors.primary,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.gray600,
  },
  activeTabText: {
    color: 'white',
  },
  content: {
    flex: 1,
  },
});
